<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\AppointmentCollection;
use App\Http\Resources\AppointmentResource;
use App\Http\Traits\ApiResponse;
use App\Models\Appointment;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AppointmentController extends Controller
{
    use ApiResponse;

    /**
     * Display a listing of appointments
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 15);
        $assistantId = $request->get('assistant_id');
        $representativeId = $request->get('representative_id');
        $source = $request->get('source');
        $appointmentType = $request->get('appointment_type');
        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');

        $query = Appointment::with(['assistant', 'representative', 'purchases', 'bonuses']);

        // Filter by assistant
        if ($assistantId) {
            $query->where('assistant_id', $assistantId);
        }

        // Filter by representative
        if ($representativeId) {
            $query->where('representative_id', $representativeId);
        }

        // Filter by source
        if ($source) {
            $query->where('source', $source);
        }

        // Filter by appointment type
        if ($appointmentType) {
            $query->where('appointment_type', $appointmentType);
        }

        // Filter by date range
        if ($dateFrom) {
            $query->whereDate('dateTime', '>=', $dateFrom);
        }
        if ($dateTo) {
            $query->whereDate('dateTime', '<=', $dateTo);
        }

        // Order by date
        $query->orderBy('dateTime', 'desc');

        $appointments = $query->paginate($perPage);

        return $this->paginatedResponse(
            new AppointmentCollection($appointments),
            'Appointments retrieved successfully'
        );
    }

    /**
     * Store a newly created appointment
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'assistant_id' => 'required|exists:users,id',
            'representative_id' => 'required|exists:users,id',
            'client_name' => 'required|string|max:255',
            'client_phone' => 'required|string|max:20',
            'client_address' => 'required|string|max:255',
            'source' => 'required|in:outbound,leboncoin',
            'date_time' => 'required|date|after:now',
            'notes' => 'nullable|string',
            'items_collection' => 'required|array',
            'appointment_type' => 'required|in:announced,not_announced',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors());
        }

        // Verify that assistant and representative exist and have correct roles
        $assistant = User::find($request->assistant_id);
        $representative = User::find($request->representative_id);

        if ($assistant->role !== 'assistant') {
            return $this->errorResponse('Selected user is not an assistant', 400);
        }

        if ($representative->role !== 'representative') {
            return $this->errorResponse('Selected user is not a representative', 400);
        }

        $appointment = Appointment::create([
            'assistant_id' => $request->assistant_id,
            'representative_id' => $request->representative_id,
            'clientName' => $request->client_name,
            'clientPhone' => $request->client_phone,
            'clientAddress' => $request->client_address,
            'source' => $request->source,
            'dateTime' => $request->date_time,
            'notes' => $request->notes,
            'itemsCollection' => $request->items_collection,
            'appointment_type' => $request->appointment_type,
        ]);

        $appointment->load(['assistant', 'representative']);

        return $this->createdResponse(
            new AppointmentResource($appointment),
            'Appointment created successfully'
        );
    }

    /**
     * Display the specified appointment
     */
    public function show(Appointment $appointment): JsonResponse
    {
        $appointment->load(['assistant', 'representative', 'purchases', 'bonuses']);

        return $this->successResponse(
            new AppointmentResource($appointment),
            'Appointment retrieved successfully'
        );
    }

    /**
     * Update the specified appointment
     */
    public function update(Request $request, Appointment $appointment): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'assistant_id' => 'sometimes|required|exists:users,id',
            'representative_id' => 'sometimes|required|exists:users,id',
            'client_name' => 'sometimes|required|string|max:255',
            'client_phone' => 'sometimes|required|string|max:20',
            'client_address' => 'sometimes|required|string|max:255',
            'source' => 'sometimes|required|in:outbound,leboncoin',
            'date_time' => 'sometimes|required|date',
            'notes' => 'nullable|string',
            'items_collection' => 'sometimes|required|array',
            'appointment_type' => 'sometimes|required|in:announced,not_announced',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors());
        }

        $updateData = [];

        if ($request->has('assistant_id')) {
            $assistant = User::find($request->assistant_id);
            if ($assistant->role !== 'assistant') {
                return $this->errorResponse('Selected user is not an assistant', 400);
            }
            $updateData['assistant_id'] = $request->assistant_id;
        }

        if ($request->has('representative_id')) {
            $representative = User::find($request->representative_id);
            if ($representative->role !== 'representative') {
                return $this->errorResponse('Selected user is not a representative', 400);
            }
            $updateData['representative_id'] = $request->representative_id;
        }

        // Map request fields to database fields
        $fieldMapping = [
            'client_name' => 'clientName',
            'client_phone' => 'clientPhone',
            'client_address' => 'clientAddress',
            'source' => 'source',
            'date_time' => 'dateTime',
            'notes' => 'notes',
            'items_collection' => 'itemsCollection',
            'appointment_type' => 'appointment_type',
        ];

        foreach ($fieldMapping as $requestField => $dbField) {
            if ($request->has($requestField)) {
                $updateData[$dbField] = $request->get($requestField);
            }
        }

        $appointment->update($updateData);
        $appointment->load(['assistant', 'representative', 'purchases', 'bonuses']);

        return $this->successResponse(
            new AppointmentResource($appointment),
            'Appointment updated successfully'
        );
    }

    /**
     * Remove the specified appointment
     */
    public function destroy(Appointment $appointment): JsonResponse
    {
        $appointment->delete();

        return $this->successResponse(null, 'Appointment deleted successfully');
    }

    /**
     * Get appointments for current user
     */
    public function myAppointments(Request $request): JsonResponse
    {
        $user = $request->user();
        $perPage = $request->get('per_page', 15);

        $query = Appointment::with(['assistant', 'representative', 'purchases', 'bonuses']);

        if ($user->role === 'assistant') {
            $query->where('assistant_id', $user->id);
        } elseif ($user->role === 'representative') {
            $query->where('representative_id', $user->id);
        } else {
            return $this->errorResponse('User role not supported for this endpoint', 400);
        }

        $query->orderBy('dateTime', 'desc');
        $appointments = $query->paginate($perPage);

        return $this->paginatedResponse(
            new AppointmentCollection($appointments),
            'User appointments retrieved successfully'
        );
    }
}
