<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Appointment extends Model
{
    /** @use HasFactory<\Database\Factories\AppointmentFactory> */
    use HasFactory;

    protected $fillable = [
        'assistant_id',
        'representative_id',
        'clientName',
        'clientPhone',
        'clientAddress',
        'source',
        'dateTime',
        'notes',
        'itemsCollection',
        'appointment_type',
    ];
    protected $casts = [
        'itemsCollection' => 'array',
    ];

    public function assistant()
    {
        return $this->belongsTo(User::class, "assistant_id");
    }

    public function representative()
    {
        return $this->belongsTo(User::class, 'representative_id');
    }
    public function purchases()
    {
        return $this->hasMany(Purchase::class);
    }

    public function purchase()
    {
        return $this->hasOne(Purchase::class);
    }

    public function bonuses()
    {
        return $this->hasMany(Bonus::class);
    }
}
